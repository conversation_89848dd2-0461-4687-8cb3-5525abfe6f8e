'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { CheckCircle, ArrowRight, School, User, AlertCircle } from 'lucide-react';
import { signIn } from 'next-auth/react';
import { useSignUpWizard } from './SignUpWizardContext';
import { Button } from '@/components/atoms/Button/Button';

export default function ConfirmationStep() {
  const { state, clearPassword } = useSignUpWizard();
  const { userData, password } = state;
  const router = useRouter();
  const [isSigningIn, setIsSigningIn] = useState(false);
  const [signInError, setSignInError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(5);

  // Auto sign-in effect
  useEffect(() => {
    const performAutoSignIn = async () => {
      if (!userData?.email || !password || isSigningIn) {
        return;
      }

      setIsSigningIn(true);
      setSignInError(null);

      try {
        const result = await signIn('credentials', {
          email: userData.email,
          password: password,
          redirect: false,
        });

        if (result?.ok) {
          // Sign-in successful, redirect to dashboard
          router.push('/my-school');
        } else {
          // Sign-in failed
          setSignInError(result?.error || 'Failed to sign in automatically');
        }
      } catch (error: any) {
        console.error('Auto sign-in error:', error);
        setSignInError('An unexpected error occurred during sign-in');
      } finally {
        // Always clear password from memory
        clearPassword();
        setIsSigningIn(false);
      }
    };

    // Start auto sign-in after a short delay to show success message
    const timer = setTimeout(performAutoSignIn, 1500);
    return () => clearTimeout(timer);
  }, [userData, password, router, clearPassword, isSigningIn]);

  // Fallback countdown for manual redirect if auto sign-in fails
  useEffect(() => {
    if (signInError) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            router.push('/auth/sign-in');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [signInError, router]);

  const handleContinue = () => {
    if (signInError) {
      router.push('/auth/sign-in');
    } else {
      router.push('/my-school');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="relative">
        {/* Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-white to-blue-50 rounded-2xl"></div>
        
        {/* Glass Effect */}
        <div className="relative bg-white/70 backdrop-blur-sm border border-white/40 rounded-2xl p-8 shadow-lg">
          <div className="text-center space-y-6">
            {/* Success Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="flex justify-center">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                  <CheckCircle size={40} className="text-white" />
                </div>
              </div>
            </motion.div>

            {/* Welcome Message */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Welcome to EduSG!
              </h2>
              <p className="text-lg text-gray-600">
                Congratulations, {userData?.name}! Your account and school have been created successfully.
              </p>
            </motion.div>

            {/* Success Details */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <User size={20} className="text-green-600" />
                    <div className="text-left">
                      <p className="font-medium text-green-800">Account Created</p>
                      <p className="text-sm text-green-600">
                        Your independent teacher account is ready
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <School size={20} className="text-blue-600" />
                    <div className="text-left">
                      <p className="font-medium text-blue-800">School Established</p>
                      <p className="text-sm text-blue-600">
                        Your school is set up and ready for students
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Next Steps */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <div className="space-y-4">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h3 className="font-medium text-gray-800 mb-2">What&apos;s Next?</h3>
                  <ul className="text-sm text-gray-600 space-y-1 text-left">
                    <li>• Customize your school profile and settings</li>
                    <li>• Add courses and educational content</li>
                    <li>• Invite students to join your school</li>
                    <li>• Start creating engaging lessons</li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <div className="space-y-3">
                {isSigningIn ? (
                  // Loading state during auto sign-in
                  <div className="space-y-4">
                    <div className="flex items-center justify-center space-x-3">
                      <span className="loading loading-spinner loading-md text-blue-600"></span>
                      <span className="text-blue-600 font-medium">Signing you in...</span>
                    </div>
                    <p className="text-sm text-gray-500">
                      Please wait while we authenticate your account
                    </p>
                  </div>
                ) : signInError ? (
                  // Error state with fallback options
                  <div className="space-y-4">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <AlertCircle size={20} className="text-red-600" />
                        <div className="text-left">
                          <p className="font-medium text-red-800">Sign-in Issue</p>
                          <p className="text-sm text-red-600">
                            {signInError}
                          </p>
                        </div>
                      </div>
                    </div>

                    <Button
                      onClick={handleContinue}
                      variant="primary"
                      className="w-full h-12 text-base font-semibold rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl focus:ring-4 focus:ring-blue-200 focus:ring-offset-2 border-0"
                    >
                      <div className="flex items-center justify-center space-x-2">
                        <span>Continue to Sign In</span>
                        <ArrowRight size={16} />
                      </div>
                    </Button>

                    <p className="text-sm text-gray-500">
                      Redirecting to sign-in page in {countdown} seconds...
                    </p>
                  </div>
                ) : (
                  // Success state - auto sign-in will happen
                  <div className="space-y-4">
                    <Button
                      onClick={handleContinue}
                      variant="primary"
                      className="w-full h-12 text-base font-semibold rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl focus:ring-4 focus:ring-blue-200 focus:ring-offset-2 border-0"
                    >
                      <div className="flex items-center justify-center space-x-2">
                        <span>Go to My School</span>
                        <ArrowRight size={16} />
                      </div>
                    </Button>

                    <p className="text-sm text-gray-500">
                      Taking you to your school dashboard...
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
