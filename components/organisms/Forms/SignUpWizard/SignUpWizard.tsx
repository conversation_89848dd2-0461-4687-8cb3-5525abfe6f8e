'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { SignUpWizardProvider, useSignUpWizard } from './SignUpWizardContext';
import UserAccountCreationStep from './UserAccountCreationStep';
import SchoolCreationStep from './SchoolCreationStep';
import ConfirmationStep from './ConfirmationStep';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';

function SignUpWizardContent() {
  const { state } = useSignUpWizard();
  const { currentStep, steps, error } = state;

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <UserAccountCreationStep />;
      case 2:
        return <SchoolCreationStep />;
      case 3:
        return <ConfirmationStep />;
      default:
        return <UserAccountCreationStep />;
    }
  };

  return (
    <div className="w-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="space-y-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Create Your Account
              </h1>
              <p className="text-gray-600">
                Join EduSG and start your educational journey
              </p>
            </div>
          </motion.div>

          {/* Progress Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <ul className="steps w-full">
              {steps.map((step) => (
                <li
                  key={step.id}
                  className={`step ${
                    step.id <= currentStep ? 'step-primary' : ''
                  }`}
                  data-content={step.id <= currentStep ? '✓' : step.id}
                >
                  <div className="text-center">
                    <div className="font-medium">{step.title}</div>
                    <div className="text-xs text-gray-500">{step.description}</div>
                  </div>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Error Alert */}
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AlertMessage
                type="error"
                message={error}
              />
            </motion.div>
          )}

          {/* Step Content */}
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            {renderStep()}
          </motion.div>

          {/* Navigation to Sign In */}
          {currentStep === 1 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <div className="text-center pt-6 border-t border-gray-200">
                <p className="text-gray-600">
                  Already have an account?{' '}
                  <Link
                    href="/auth/sign-in"
                    className="text-blue-600 hover:text-blue-700 font-medium transition-colors"
                  >
                    Sign in here
                  </Link>
                </p>
              </div>
            </motion.div>
          )}
        </div>
      </motion.div>
    </div>
  );
}

export default function SignUpWizard() {
  return (
    <SignUpWizardProvider>
      <SignUpWizardContent />
    </SignUpWizardProvider>
  );
}
