'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion } from 'framer-motion';
import { Mail, Lock, User } from 'lucide-react';
import { signIn } from 'next-auth/react';
import { useSignUpWizard } from './SignUpWizardContext';
import { signUpAction } from '@/actions/user.action';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

// Validation schema
const userAccountSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name is too long'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

type UserAccountFormData = z.infer<typeof userAccountSchema>;

export default function UserAccountCreationStep() {
  const { state, nextStep, setUserData, setPassword, setLoading, setError } = useSignUpWizard();
  const { isLoading } = state;

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<UserAccountFormData>({
    resolver: zodResolver(userAccountSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: UserAccountFormData) => {
    try {
      setLoading(true);
      setError(null);

      // Create FormData for server action
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('email', data.email);
      formData.append('password', data.password);

      const result = await signUpAction(formData);

      if (result.status === 'success' && result.data) {
        setUserData(result.data);
        // Store password for auto sign-in after school creation
        setPassword(data.password);

        // Automatically sign in the user after successful account creation
        const signInResult = await signIn('credentials', {
          email: data.email,
          password: data.password,
          redirect: false,
        });

        if (signInResult?.ok) {
          // Sign-in successful, proceed to next step
          nextStep();
        } else {
          // Sign-in failed, but account was created
          setError('Account created successfully, but automatic sign-in failed. Please continue to create your school.');
          nextStep(); // Still proceed to next step
        }
      } else {
        // Handle error response - result is TApiError when status is 'error'
        const errorMessage = (result as any).message || 'Failed to create account';
        setError(errorMessage);
      }
    } catch (error: any) {
      console.error('Error creating account:', error);
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="relative">
        {/* Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-2xl"></div>
        
        {/* Glass Effect */}
        <div className="relative bg-white/70 backdrop-blur-sm border border-white/40 rounded-2xl p-8 shadow-lg">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Name Field */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <FormField
                label="Full Name"
                error={errors.name?.message}
                required
              >
                <InputWithIcon
                  type="text"
                  placeholder="Enter your full name"
                  leftIcon={<User size={18} className="text-blue-500" />}
                  className={cn(
                    'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                    'bg-white/50 backdrop-blur-sm',
                    errors.name
                      ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                      : 'border-gray-200 focus:border-blue-500'
                  )}
                  {...register('name')}
                  disabled={isLoading}
                />
              </FormField>
            </motion.div>

            {/* Email Field */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <FormField
                label="Email Address"
                error={errors.email?.message}
                required
              >
                <InputWithIcon
                  type="email"
                  placeholder="Enter your email address"
                  leftIcon={<Mail size={18} className="text-blue-500" />}
                  className={cn(
                    'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                    'bg-white/50 backdrop-blur-sm',
                    errors.email
                      ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                      : 'border-gray-200 focus:border-blue-500'
                  )}
                  {...register('email')}
                  disabled={isLoading}
                />
              </FormField>
            </motion.div>

            {/* Password Field */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <FormField
                label="Password"
                error={errors.password?.message}
                required
              >
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <Lock size={18} className="text-blue-500" />
                  </div>
                  <PasswordInput
                    placeholder="Create a strong password"
                    hasLeftIcon={true}
                    className={cn(
                      'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                      'bg-white/50 backdrop-blur-sm',
                      errors.password
                        ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                        : 'border-gray-200 focus:border-blue-500'
                    )}
                    {...register('password')}
                    disabled={isLoading}
                  />
                </div>
              </FormField>
            </motion.div>

            {/* Confirm Password Field */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <FormField
                label="Confirm Password"
                error={errors.confirmPassword?.message}
                required
              >
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <Lock size={18} className="text-blue-500" />
                  </div>
                  <PasswordInput
                    placeholder="Confirm your password"
                    hasLeftIcon={true}
                    className={cn(
                      'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                      'bg-white/50 backdrop-blur-sm',
                      errors.confirmPassword
                        ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                        : 'border-gray-200 focus:border-blue-500'
                    )}
                    {...register('confirmPassword')}
                    disabled={isLoading}
                  />
                </div>
              </FormField>
            </motion.div>

            {/* Submit Button */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <Button
                type="submit"
                variant="primary"
                className="w-full h-12 text-base font-semibold rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl focus:ring-4 focus:ring-blue-200 focus:ring-offset-2 border-0"
                isLoading={isLoading}
                disabled={isLoading}
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </Button>
            </motion.div>
          </form>
        </div>
      </div>
    </motion.div>
  );
}
