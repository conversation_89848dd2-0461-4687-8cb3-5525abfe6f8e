'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useSignUpWizard } from './SignUpWizardContext';
import { SchoolCreationForm } from '@/components/organisms/SchoolCreationForm';
import { Button } from '@/components/atoms/Button/Button';
import { ArrowLeft } from 'lucide-react';

export default function SchoolCreationStep() {
  const { state, previousStep, nextStep, setError } = useSignUpWizard();
  const { userData } = state;

  const handleSchoolCreated = () => {
    // Move to confirmation step when school is successfully created
    nextStep();
  };

  const handleSchoolCreationError = (error: string) => {
    setError(error);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Create Your School
          </h2>
          <p className="text-gray-600">
            Welcome, {userData?.name}! Now let&apos;s set up your school details.
          </p>
        </div>

        {/* Navigation */}
        <div className="flex justify-start">
          <Button
            variant="ghost"
            onClick={previousStep}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft size={16} />
            <span>Back to Account Details</span>
          </Button>
        </div>

        {/* School Creation Form */}
        <div className="relative">
          {/* Gradient Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-2xl"></div>
          
          {/* Glass Effect */}
          <div className="relative bg-white/70 backdrop-blur-sm border border-white/40 rounded-2xl p-8 shadow-lg">
            <SchoolCreationForm
              onSuccess={handleSchoolCreated}
              onError={handleSchoolCreationError}
            />
          </div>
        </div>
      </div>
    </motion.div>
  );
}
